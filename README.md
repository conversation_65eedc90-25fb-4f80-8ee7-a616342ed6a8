# Surf Malaga Data Science Project

This project is a simple experiment, taking historical wind and wave data in the form of windguru page HTML, and seeing if we can build a model that predicts surf the wavesthe waves. The input is a raw save of the Windguru page.

### Rough outline of the process

1. Extract the data from the HTML
2. Clean the data
3. Explore model options:
    1. DBSCAN / HDBSCAN - Surfable days form rare clusters
    2. Autoencoder / VAE - Surfable days = anomaly
    3. Isolation Forest - Data is tabular + rare events
    4. Contrastive Learning - You want surf-specific embeddings
    5. LSTM Sequence Embedding - Capturing temporal build-up

## Project Structure

```
surf_malaga/
├── README.md              # Project documentation
├── pyproject.toml          # Project configuration and dependencies
├── requirements.lock       # Locked dependencies
├── requirements-dev.lock   # Locked dev dependencies
├── .venv/                  # Virtual environment (managed by Rye)
├── notebooks/              # Jupyter notebooks for analysis
├── data/                   # Data files
│   ├── raw/               # Raw, immutable data
│   ├── processed/         # Cleaned and processed data
│   └── external/          # External data sources
├── src/                   # Source code modules
│   └── surf_malaga/       # Main package
├── tests/                 # Test files
├── docs/                  # Documentation
└── files/                 # Project files (existing)
```

## Setup

This project uses [Rye](https://rye-up.com/) for Python package management.

### Prerequisites
- Python 3.12
- Rye package manager

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   rye sync
   ```

### Running Jupyter

To start Jupyter Lab:
```bash
rye run jupyter lab
```

To start Jupyter Notebook:
```bash
rye run jupyter notebook
```


## Usage

1. Place raw data files in `data/raw/`
2. Create analysis notebooks in `notebooks/`
3. Develop reusable code in `src/surf_malaga/`
4. Write tests in `tests/`

## Data

The project includes surf condition data for Malaga:
- Wind data from Windguru
- Wave data from Windguru
- Screenshots of conditions

## Contributing

1. Create feature branches for new analysis
2. Write tests for any reusable functions
3. Document your analysis in notebooks
4. Update this README as needed
