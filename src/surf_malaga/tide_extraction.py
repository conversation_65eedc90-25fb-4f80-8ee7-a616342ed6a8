"""
Tide data extraction from PDF files.

This module provides functions to extract tide data from Sotogrande PDF files
and process it for alignment with wind/wave data intervals.
"""

import pandas as pd
import numpy as np
import pdfplumber
import re
from datetime import datetime, timedelta
from pathlib import Path
# Removed CubicSpline import - using linear interpolation instead


def extract_tide_data_from_pdf(pdf_path):
    """
    Extract tide data from a single PDF file.
    
    Args:
        pdf_path (Path): Path to the PDF file
        
    Returns:
        list: List of dictionaries with datetime and tide_height_m
    """
    tide_data = []
    
    # Extract year from filename (e.g., "Sotogrande_2024_1.pdf" -> 2024)
    year_match = re.search(r'(\d{4})', pdf_path.name)
    if not year_match:
        print(f"Warning: Could not extract year from {pdf_path.name}")
        return tide_data
    
    year = int(year_match.group(1))
    print(f"\nProcessing {pdf_path.name} (Year: {year})")
    
    with pdfplumber.open(pdf_path) as pdf:
        for page_num, page in enumerate(pdf.pages):
            print(f"  Processing page {page_num + 1}")
            
            # Extract text to identify months
            text = page.extract_text()
            if not text:
                continue
                
            # Find month names in the text
            month_names = ['ENERO', 'FEBRERO', 'MARZO', 'ABRIL', 'MAYO', 'JUNIO',
                          'JULIO', 'AGOSTO', 'SEPTIEMBRE', 'OCTUBRE', 'NOVIEMBRE', 'DICIEMBRE']
            
            months_found = []
            for i, month_name in enumerate(month_names, 1):
                if month_name in text:
                    months_found.append(i)
            
            print(f"    Found months: {months_found}")
            
            # Extract tables
            tables = page.find_tables()
            if not tables:
                print(f"    No tables found on page {page_num + 1}")
                continue
                
            for table in tables:
                table_data = table.extract()
                if not table_data:
                    continue
                    
                # Process table data
                cell_records = parse_tide_table(table_data, year, months_found)
                # Skip validation for now - focus on fixing parsing bugs
                tide_data.extend(cell_records)
    
    return tide_data


def parse_tide_table(table_data, year, months_found):
    """
    Parse the complex nested tide table structure.

    The table structure has 8 columns for 4 months:
    - Column 0: Month 1 (combined data for days 1-15 & 16-31)
    - Column 1: Month 1 (days 16-31 only) - skip this duplicate
    - Column 2: Month 2 (combined data for days 1-15 & 16-31)
    - Column 3: Month 2 (days 16-31 only) - skip this duplicate
    - etc.

    Args:
        table_data (list): Raw table data from pdfplumber
        year (int): Year for the data
        months_found (list): List of month numbers found on the page

    Returns:
        list: List of dictionaries with datetime and tide_height_m
    """
    tide_records = []

    if not table_data or len(table_data) < 2:
        return tide_records

    # Skip header row and process data rows
    for row in table_data[1:]:
        if not row or not any(row):
            continue

        # Process only the even-numbered columns (0, 2, 4, 6) which contain complete month data
        # Skip odd-numbered columns (1, 3, 5, 7) which contain duplicate partial data
        for month_idx, month in enumerate(months_found):
            col_idx = month_idx * 2  # Column 0, 2, 4, 6...

            if col_idx >= len(row):
                continue

            cell = row[col_idx]
            if not cell or cell.strip() == '':
                continue

            # Parse tide data from cell (contains data for the entire month)
            cell_records = parse_tide_cell_corrected(cell, year, month)
            tide_records.extend(cell_records)

    return tide_records


def parse_tide_cell_corrected(cell_text, year, month):
    """
    Parse individual cell containing tide data for an entire month.

    The structure follows a strict pattern for each day pair (1&16, 2&17, etc.):
    - Line N+0: First tides for both days (no day numbers)
    - Line N+1: Day numbers + second tides
    - Line N+2: Third tides for both days
    - Line N+3: Weekday letters (may have more tides)
    - Line N+4: Fourth tide (usually only for first day of pair)

    Args:
        cell_text (str): Raw cell text for entire month
        year (int): Year
        month (int): Month number (1-12)

    Returns:
        list: List of tide records
    """
    records = []

    if not cell_text:
        return records

    lines = cell_text.strip().split('\n')
    i = 0

    while i < len(lines):
        # Look for a line with day numbers to identify a day pair
        day_line_idx = None
        day_pair = None

        # Search forward for the next line with day numbers
        # Day numbers are followed by time patterns like "1 11:35 0,80 16 11:59 0,87"
        for j in range(i, len(lines)):
            line = lines[j].strip()
            day_match = re.search(r'(\d{1,2})\s+\d{1,2}:\d{2}\s+\d+,\d+\s+(\d{1,2})\s+\d{1,2}:\d{2}', line)
            if day_match:
                day_line_idx = j
                day_pair = (int(day_match.group(1)), int(day_match.group(2)))
                break

        if not day_pair:
            break

        # Process the group of lines for this day pair
        # The pattern is typically 4-5 lines per day pair
        group_start = max(0, day_line_idx - 1)  # Line before day numbers
        group_end = min(len(lines), day_line_idx + 4)  # A few lines after

        # Collect all tide times for this day pair
        day1_tides = []
        day2_tides = []

        for line_idx in range(group_start, group_end):
            if line_idx >= len(lines):
                break

            line = lines[line_idx].strip()
            if not line or line.isalpha():  # Skip empty lines and weekday letters
                continue

            # Extract all time-height pairs from this line
            time_height_pattern = r'(\d{1,2}:\d{2})\s+(\d+,\d+)'
            matches = re.findall(time_height_pattern, line)

            if not matches:
                continue

            # Assign matches to day1 and day2 alternately
            # But be smart about single tides - they usually belong to the first day
            if len(matches) == 1:
                # Single tide - assign to day1 unless it's clearly for day2
                time_str, height_str = matches[0]
                try:
                    hour, minute = map(int, time_str.split(':'))
                    height = float(height_str.replace(',', '.'))

                    # Single tides usually belong to day1 (the first day of the pair)
                    # Only assign to day2 if day1 already has 4 tides (very rare)
                    if len(day1_tides) >= 4:
                        day2_tides.append((hour, minute, height))
                    else:
                        day1_tides.append((hour, minute, height))

                except (ValueError, TypeError):
                    continue
            else:
                # Multiple tides - assign alternately
                for match_idx, (time_str, height_str) in enumerate(matches):
                    try:
                        hour, minute = map(int, time_str.split(':'))
                        height = float(height_str.replace(',', '.'))

                        if match_idx % 2 == 0:
                            day1_tides.append((hour, minute, height))
                        else:
                            day2_tides.append((hour, minute, height))

                    except (ValueError, TypeError):
                        continue

        # Create records for day1
        for hour, minute, height in day1_tides:
            try:
                dt = datetime(year, month, day_pair[0], hour, minute)
                records.append({
                    'datetime': dt,
                    'date': dt.date(),
                    'time': f"{hour:02d}:{minute:02d}",
                    'tide_height_m': height
                })
            except ValueError:
                continue

        # Create records for day2
        for hour, minute, height in day2_tides:
            try:
                dt = datetime(year, month, day_pair[1], hour, minute)
                records.append({
                    'datetime': dt,
                    'date': dt.date(),
                    'time': f"{hour:02d}:{minute:02d}",
                    'tide_height_m': height
                })
            except ValueError:
                continue

        # Move to next day pair
        i = group_end

    # After processing all day pairs, look for day 31 data at the end
    # Day 31 appears as a separate section after all the pairs
    day_31_records = parse_day_31_section(lines, year, month)
    records.extend(day_31_records)

    return records


def parse_day_31_section(lines, year, month):
    """
    Parse the separate section containing day 31 data.

    Day 31 appears at the end of the month column in a separate section:
    - Line N: First tide time (no day number) - 05:08 0,30
    - Line N+1: "31 HH:MM H,HH" (day number with second tide) - 31 11:31 0,77
    - Line N+2: Third tide time - 17:15 0,32
    - Line N+3: Weekday letter with fourth tide - X 23:48 0,82

    Args:
        lines (list): All lines from the month cell
        year (int): Year
        month (int): Month number

    Returns:
        list: List of tide records for day 31
    """
    records = []

    # Look for the line containing "31 HH:MM" pattern
    day_31_line_idx = None
    for i, line in enumerate(lines):
        if re.search(r'\b31\s+\d{1,2}:\d{2}', line):
            day_31_line_idx = i
            break

    if day_31_line_idx is None:
        return records  # No day 31 in this month

    # Process exactly the day 31 section (the line before day 31 line + 3 lines after)
    # But be more careful about which tides belong to day 31
    day_31_tides = []

    # Line before day 31 line (first tide for day 31)
    if day_31_line_idx > 0:
        prev_line = lines[day_31_line_idx - 1].strip()
        time_height_pattern = r'(\d{1,2}:\d{2})\s+(\d+,\d+)'
        matches = re.findall(time_height_pattern, prev_line)

        # Only take the FIRST tide from this line (it should be day 31's first tide)
        if matches:
            time_str, height_str = matches[0]  # Only first match
            try:
                hour, minute = map(int, time_str.split(':'))
                height = float(height_str.replace(',', '.'))
                day_31_tides.append((hour, minute, height))
            except (ValueError, TypeError):
                pass

    # Day 31 line itself (contains day number + second tide)
    day_31_line = lines[day_31_line_idx].strip()
    time_height_pattern = r'(\d{1,2}:\d{2})\s+(\d+,\d+)'
    matches = re.findall(time_height_pattern, day_31_line)
    for time_str, height_str in matches:
        try:
            hour, minute = map(int, time_str.split(':'))
            height = float(height_str.replace(',', '.'))
            day_31_tides.append((hour, minute, height))
        except (ValueError, TypeError):
            continue

    # Next 2 lines after day 31 line (third and fourth tides)
    for offset in [1, 2]:
        line_idx = day_31_line_idx + offset
        if line_idx < len(lines):
            line = lines[line_idx].strip()
            time_height_pattern = r'(\d{1,2}:\d{2})\s+(\d+,\d+)'
            matches = re.findall(time_height_pattern, line)

            for time_str, height_str in matches:
                try:
                    hour, minute = map(int, time_str.split(':'))
                    height = float(height_str.replace(',', '.'))
                    day_31_tides.append((hour, minute, height))
                except (ValueError, TypeError):
                    continue

    # Create records for day 31
    for hour, minute, height in day_31_tides:
        try:
            # Check if this month actually has 31 days
            dt = datetime(year, month, 31, hour, minute)
            records.append({
                'datetime': dt,
                'date': dt.date(),
                'time': f"{hour:02d}:{minute:02d}",
                'tide_height_m': height
            })
        except ValueError:
            # Month doesn't have 31 days, skip
            continue

    return records


def validate_tide_sequence(records):
    """
    Validate that tide records follow a sensible temporal sequence.

    Args:
        records (list): List of tide record dictionaries

    Returns:
        list: Filtered list with invalid records removed
    """
    if not records:
        return records

    # Sort by datetime
    sorted_records = sorted(records, key=lambda x: x['datetime'])
    validated_records = [sorted_records[0]]  # Always keep first record

    for i in range(1, len(sorted_records)):
        current = sorted_records[i]
        previous = validated_records[-1]

        time_diff = current['datetime'] - previous['datetime']
        hours_diff = time_diff.total_seconds() / 3600

        # Tides typically occur every 6-12 hours
        # Allow some flexibility but reject obvious errors
        if 4 <= hours_diff <= 18:  # Reasonable tide interval
            validated_records.append(current)
        elif hours_diff < 4:
            # Check if this might be a valid 4th tide of the day (can be closer)
            prev_date = previous['datetime'].date()
            curr_date = current['datetime'].date()

            if curr_date == prev_date and hours_diff >= 2:
                # Same day and at least 2 hours apart - might be valid 4th tide
                validated_records.append(current)
            else:
                # Too close - might be duplicate, skip
                print(f"Skipping tide too close in time: {current['datetime']} (only {hours_diff:.1f}h after previous)")
                continue
        elif hours_diff > 18:
            # Too far - might be misassigned day, but keep it for now
            # (interpolation will handle gaps)
            validated_records.append(current)
        else:
            validated_records.append(current)

    return validated_records


def interpolate_tide_data(tide_df, start_date, end_date):
    """
    Interpolate tide data to 2-hour intervals to match wind/wave data.

    Args:
        tide_df (pd.DataFrame): DataFrame with datetime and tide_height_m columns
        start_date (datetime): Start date for interpolation
        end_date (datetime): End date for interpolation

    Returns:
        pd.DataFrame: Interpolated tide data at 2-hour intervals
    """
    if tide_df.empty:
        return pd.DataFrame()

    # Sort by datetime
    tide_df = tide_df.sort_values('datetime').copy()

    # Validate raw tide data range (Mediterranean tides are typically 0-2m)
    raw_min = tide_df['tide_height_m'].min()
    raw_max = tide_df['tide_height_m'].max()
    print(f"Raw tide range: {raw_min:.2f}m to {raw_max:.2f}m")

    if raw_min < -1 or raw_max > 3:
        print(f"Warning: Raw tide range seems unusual for Mediterranean ({raw_min:.2f}m to {raw_max:.2f}m)")

    # Create 2-hour interval timestamps
    time_intervals = []
    current_time = start_date.replace(hour=0, minute=0, second=0, microsecond=0)

    while current_time <= end_date:
        time_intervals.append(current_time)
        current_time += timedelta(hours=2)

    # Convert to numeric for interpolation
    tide_df['timestamp'] = tide_df['datetime'].astype(np.int64) // 10**9
    target_timestamps = [dt.timestamp() for dt in time_intervals]

    # Use linear interpolation instead of cubic spline to avoid overshooting
    # Cubic splines can create extreme oscillations with sparse tidal data
    interpolated_heights = np.interp(target_timestamps,
                                   tide_df['timestamp'],
                                   tide_df['tide_height_m'])

    # Validate interpolated range (should be close to input range)
    interp_min = interpolated_heights.min()
    interp_max = interpolated_heights.max()

    print(f"Interpolation results: {interp_min:.2f}m to {interp_max:.2f}m")

    # Sanity check - interpolated values should be within reasonable bounds of input
    if interp_min < raw_min - 0.1 or interp_max > raw_max + 0.1:
        print(f"Warning: Interpolation produced values outside input range!")
        print(f"Input range: {raw_min:.2f}m to {raw_max:.2f}m")
        print(f"Output range: {interp_min:.2f}m to {interp_max:.2f}m")
        print("This suggests a bug in the interpolation logic.")

    # Create result DataFrame
    result_df = pd.DataFrame({
        'datetime': time_intervals,
        'date': [dt.date() for dt in time_intervals],
        'time': [f"{dt.hour:02d}h" for dt in time_intervals],
        'tide_height_m': interpolated_heights
    })

    return result_df


def extract_all_tide_data(tides_dir_path):
    """
    Extract tide data from all PDF files in the tides directory.
    
    Args:
        tides_dir_path (Path): Path to directory containing tide PDF files
        
    Returns:
        pd.DataFrame: Combined tide data from all files
    """
    all_tide_data = []
    
    pdf_files = list(tides_dir_path.glob('*.pdf'))
    print(f"Found {len(pdf_files)} PDF files to process")
    
    for pdf_file in sorted(pdf_files):
        tide_data = extract_tide_data_from_pdf(pdf_file)
        all_tide_data.extend(tide_data)
        print(f"  Extracted {len(tide_data)} tide records from {pdf_file.name}")
    
    if not all_tide_data:
        print("No tide data extracted")
        return pd.DataFrame()
    
    # Convert to DataFrame
    tide_df = pd.DataFrame(all_tide_data)
    
    # Remove duplicates and sort
    tide_df = tide_df.drop_duplicates(subset=['datetime']).sort_values('datetime')
    
    print(f"\nTotal tide records: {len(tide_df)}")
    print(f"Date range: {tide_df['datetime'].min()} to {tide_df['datetime'].max()}")
    
    return tide_df
