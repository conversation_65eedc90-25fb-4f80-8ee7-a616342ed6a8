"""
Tide data extraction from PDF files.

This module provides functions to extract tide data from Sotogrande PDF files
and process it for alignment with wind/wave data intervals.
"""

import pandas as pd
import numpy as np
import pdfplumber
import re
from datetime import datetime, timedelta
from pathlib import Path
from scipy.interpolate import CubicSpline


def extract_tide_data_from_pdf(pdf_path):
    """
    Extract tide data from a single PDF file.
    
    Args:
        pdf_path (Path): Path to the PDF file
        
    Returns:
        list: List of dictionaries with datetime and tide_height_m
    """
    tide_data = []
    
    # Extract year from filename (e.g., "Sotogrande_2024_1.pdf" -> 2024)
    year_match = re.search(r'(\d{4})', pdf_path.name)
    if not year_match:
        print(f"Warning: Could not extract year from {pdf_path.name}")
        return tide_data
    
    year = int(year_match.group(1))
    print(f"\nProcessing {pdf_path.name} (Year: {year})")
    
    with pdfplumber.open(pdf_path) as pdf:
        for page_num, page in enumerate(pdf.pages):
            print(f"  Processing page {page_num + 1}")
            
            # Extract text to identify months
            text = page.extract_text()
            if not text:
                continue
                
            # Find month names in the text
            month_names = ['ENERO', 'FEBRERO', 'MARZO', 'ABRIL', 'MAYO', 'JUNIO',
                          'JULIO', 'AGOSTO', 'SEPTIEMBRE', 'OCTUBRE', 'NOVIEMBRE', 'DICIEMBRE']
            
            months_found = []
            for i, month_name in enumerate(month_names, 1):
                if month_name in text:
                    months_found.append(i)
            
            print(f"    Found months: {months_found}")
            
            # Extract tables
            tables = page.find_tables()
            if not tables:
                print(f"    No tables found on page {page_num + 1}")
                continue
                
            for table in tables:
                table_data = table.extract()
                if not table_data:
                    continue
                    
                # Process table data
                cell_records = parse_tide_table(table_data, year, months_found)
                validated_records = validate_tide_sequence(cell_records)
                tide_data.extend(validated_records)
    
    return tide_data


def parse_tide_table(table_data, year, months_found):
    """
    Parse the complex nested tide table structure.

    The table structure is:
    - Each column represents a month
    - Within each column, data is organized vertically by day pairs (1&16, 2&17, etc.)
    - Each cell contains tide data for two days of the month

    Args:
        table_data (list): Raw table data from pdfplumber
        year (int): Year for the data
        months_found (list): List of month numbers found on the page

    Returns:
        list: List of dictionaries with datetime and tide_height_m
    """
    tide_records = []

    if not table_data or len(table_data) < 2:
        return tide_records

    # Skip header row and process data rows
    for row in table_data[1:]:
        if not row or not any(row):
            continue

        # Each column represents a month
        for col_idx, cell in enumerate(row):
            if not cell or cell.strip() == '':
                continue

            # Determine which month this column represents
            if col_idx < len(months_found):
                month = months_found[col_idx]
            else:
                continue

            # Parse tide data from cell (contains data for the entire month)
            cell_records = parse_tide_cell_corrected(cell, year, month)
            tide_records.extend(cell_records)

    return tide_records


def parse_tide_cell_corrected(cell_text, year, month):
    """
    Parse individual cell containing tide data for an entire month.

    The structure follows a strict pattern for each day pair (1&16, 2&17, etc.):
    - Line N+0: First tides for both days (no day numbers)
    - Line N+1: Day numbers + second tides
    - Line N+2: Third tides for both days
    - Line N+3: Weekday letters (may have more tides)
    - Line N+4: Fourth tide (usually only for first day of pair)

    Args:
        cell_text (str): Raw cell text for entire month
        year (int): Year
        month (int): Month number (1-12)

    Returns:
        list: List of tide records
    """
    records = []

    if not cell_text:
        return records

    lines = cell_text.strip().split('\n')
    i = 0

    while i < len(lines):
        # Look for a line with day numbers to identify a day pair
        day_line_idx = None
        day_pair = None

        # Search forward for the next line with day numbers
        # Day numbers are followed by time patterns like "1 11:35 0,80 16 11:59 0,87"
        for j in range(i, len(lines)):
            line = lines[j].strip()
            day_match = re.search(r'(\d{1,2})\s+\d{1,2}:\d{2}\s+\d+,\d+\s+(\d{1,2})\s+\d{1,2}:\d{2}', line)
            if day_match:
                day_line_idx = j
                day_pair = (int(day_match.group(1)), int(day_match.group(2)))
                break

        if not day_pair:
            break

        # Process the group of lines for this day pair
        # The pattern is typically 4-5 lines per day pair
        group_start = max(0, day_line_idx - 1)  # Line before day numbers
        group_end = min(len(lines), day_line_idx + 4)  # A few lines after

        # Collect all tide times for this day pair
        day1_tides = []
        day2_tides = []

        for line_idx in range(group_start, group_end):
            if line_idx >= len(lines):
                break

            line = lines[line_idx].strip()
            if not line or line.isalpha():  # Skip empty lines and weekday letters
                continue

            # Extract all time-height pairs from this line
            time_height_pattern = r'(\d{1,2}:\d{2})\s+(\d+,\d+)'
            matches = re.findall(time_height_pattern, line)

            if not matches:
                continue

            # Assign matches to day1 and day2 alternately
            # But be smart about single tides - they usually belong to the first day
            if len(matches) == 1:
                # Single tide - assign to day1 unless it's clearly for day2
                time_str, height_str = matches[0]
                try:
                    hour, minute = map(int, time_str.split(':'))
                    height = float(height_str.replace(',', '.'))

                    # Single tides usually belong to day1 (the first day of the pair)
                    # Only assign to day2 if day1 already has 4 tides (very rare)
                    if len(day1_tides) >= 4:
                        day2_tides.append((hour, minute, height))
                    else:
                        day1_tides.append((hour, minute, height))

                except (ValueError, TypeError):
                    continue
            else:
                # Multiple tides - assign alternately
                for match_idx, (time_str, height_str) in enumerate(matches):
                    try:
                        hour, minute = map(int, time_str.split(':'))
                        height = float(height_str.replace(',', '.'))

                        if match_idx % 2 == 0:
                            day1_tides.append((hour, minute, height))
                        else:
                            day2_tides.append((hour, minute, height))

                    except (ValueError, TypeError):
                        continue

        # Create records for day1
        for hour, minute, height in day1_tides:
            try:
                dt = datetime(year, month, day_pair[0], hour, minute)
                records.append({
                    'datetime': dt,
                    'date': dt.date(),
                    'time': f"{hour:02d}:{minute:02d}",
                    'tide_height_m': height
                })
            except ValueError:
                continue

        # Create records for day2
        for hour, minute, height in day2_tides:
            try:
                dt = datetime(year, month, day_pair[1], hour, minute)
                records.append({
                    'datetime': dt,
                    'date': dt.date(),
                    'time': f"{hour:02d}:{minute:02d}",
                    'tide_height_m': height
                })
            except ValueError:
                continue

        # Move to next day pair
        i = group_end

    return records


def validate_tide_sequence(records):
    """
    Validate that tide records follow a sensible temporal sequence.

    Args:
        records (list): List of tide record dictionaries

    Returns:
        list: Filtered list with invalid records removed
    """
    if not records:
        return records

    # Sort by datetime
    sorted_records = sorted(records, key=lambda x: x['datetime'])
    validated_records = [sorted_records[0]]  # Always keep first record

    for i in range(1, len(sorted_records)):
        current = sorted_records[i]
        previous = validated_records[-1]

        time_diff = current['datetime'] - previous['datetime']
        hours_diff = time_diff.total_seconds() / 3600

        # Tides typically occur every 6-12 hours
        # Allow some flexibility but reject obvious errors
        if 4 <= hours_diff <= 18:  # Reasonable tide interval
            validated_records.append(current)
        elif hours_diff < 4:
            # Check if this might be a valid 4th tide of the day (can be closer)
            prev_date = previous['datetime'].date()
            curr_date = current['datetime'].date()

            if curr_date == prev_date and hours_diff >= 2:
                # Same day and at least 2 hours apart - might be valid 4th tide
                validated_records.append(current)
            else:
                # Too close - might be duplicate, skip
                print(f"Skipping tide too close in time: {current['datetime']} (only {hours_diff:.1f}h after previous)")
                continue
        elif hours_diff > 18:
            # Too far - might be misassigned day, but keep it for now
            # (interpolation will handle gaps)
            validated_records.append(current)
        else:
            validated_records.append(current)

    return validated_records


def interpolate_tide_data(tide_df, start_date, end_date):
    """
    Interpolate tide data to 2-hour intervals to match wind/wave data.
    
    Args:
        tide_df (pd.DataFrame): DataFrame with datetime and tide_height_m columns
        start_date (datetime): Start date for interpolation
        end_date (datetime): End date for interpolation
        
    Returns:
        pd.DataFrame: Interpolated tide data at 2-hour intervals
    """
    if tide_df.empty:
        return pd.DataFrame()
    
    # Sort by datetime
    tide_df = tide_df.sort_values('datetime').copy()
    
    # Create 2-hour interval timestamps
    time_intervals = []
    current_time = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
    
    while current_time <= end_date:
        time_intervals.append(current_time)
        current_time += timedelta(hours=2)
    
    # Convert to numeric for interpolation
    tide_df['timestamp'] = tide_df['datetime'].astype(np.int64) // 10**9
    target_timestamps = [dt.timestamp() for dt in time_intervals]
    
    # Perform cubic spline interpolation
    if len(tide_df) >= 4:  # Need at least 4 points for cubic spline
        cs = CubicSpline(tide_df['timestamp'], tide_df['tide_height_m'])
        interpolated_heights = cs(target_timestamps)
    else:
        # Fall back to linear interpolation for fewer points
        interpolated_heights = np.interp(target_timestamps, 
                                       tide_df['timestamp'], 
                                       tide_df['tide_height_m'])
    
    # Create result DataFrame
    result_df = pd.DataFrame({
        'datetime': time_intervals,
        'date': [dt.date() for dt in time_intervals],
        'time': [f"{dt.hour:02d}h" for dt in time_intervals],
        'tide_height_m': interpolated_heights
    })
    
    return result_df


def extract_all_tide_data(tides_dir_path):
    """
    Extract tide data from all PDF files in the tides directory.
    
    Args:
        tides_dir_path (Path): Path to directory containing tide PDF files
        
    Returns:
        pd.DataFrame: Combined tide data from all files
    """
    all_tide_data = []
    
    pdf_files = list(tides_dir_path.glob('*.pdf'))
    print(f"Found {len(pdf_files)} PDF files to process")
    
    for pdf_file in sorted(pdf_files):
        tide_data = extract_tide_data_from_pdf(pdf_file)
        all_tide_data.extend(tide_data)
        print(f"  Extracted {len(tide_data)} tide records from {pdf_file.name}")
    
    if not all_tide_data:
        print("No tide data extracted")
        return pd.DataFrame()
    
    # Convert to DataFrame
    tide_df = pd.DataFrame(all_tide_data)
    
    # Remove duplicates and sort
    tide_df = tide_df.drop_duplicates(subset=['datetime']).sort_values('datetime')
    
    print(f"\nTotal tide records: {len(tide_df)}")
    print(f"Date range: {tide_df['datetime'].min()} to {tide_df['datetime'].max()}")
    
    return tide_df
