import pandas as pd
import sys
from datetime import datetime
from pathlib import Path

# Set up paths
project_root = Path().resolve().parent
data_raw_path = project_root / 'data' / 'raw'
data_processed_path = project_root / 'data' / 'processed'

# Add src to path for imports
sys.path.append(str(project_root / 'src'))

# Import our extraction functions
from surf_malaga.data_extraction import (
    extract_wave_data_complete,
    extract_wind_data_complete,
    combine_and_complete_data
)

# Ensure processed data directory exists
data_processed_path.mkdir(parents=True, exist_ok=True)

print(f"Project root: {project_root}")
print(f"Raw data path: {data_raw_path}")
print(f"Processed data path: {data_processed_path}")

# Extract wave data using the complete extraction function
wave_file = data_raw_path / 'Windguru_El_Palo_Wave_2h_2023-07-01_2025-07-11.html'
print(f"Extracting wave data from: {wave_file}")

wave_df = extract_wave_data_complete(wave_file)
print(f"Extracted {len(wave_df)} wave data records")
print(f"Date range: {wave_df['datetime'].min()} to {wave_df['datetime'].max()}")
print(f"Missing values in wave data: {wave_df.isnull().sum().sum()}")
wave_df.head()

# Extract wind data using the complete extraction function
wind_file = data_raw_path / 'Windguru_El_Palo_Wind_2h_2023-07-01_2025-07-11.html'
print(f"Extracting wind data from: {wind_file}")

wind_df = extract_wind_data_complete(wind_file)
print(f"Extracted {len(wind_df)} wind data records")
print(f"Date range: {wind_df['datetime'].min()} to {wind_df['datetime'].max()}")
print(f"Missing values in wind data: {wind_df.isnull().sum().sum()}")
wind_df.head()

# Combine wind and wave data with 100% completeness
combined_df_complete = combine_and_complete_data(wave_df, wind_df)

print(f"Combined dataset shape: {combined_df_complete.shape}")
print(f"Date range: {combined_df_complete['datetime'].min()} to {combined_df_complete['datetime'].max()}")
print(f"\nColumns: {list(combined_df_complete.columns)}")

# Check final completeness
missing_values = combined_df_complete.isnull().sum().sum()
total_cells = len(combined_df_complete) * len(combined_df_complete.columns)
completeness = (1 - missing_values / total_cells) * 100

print(f"\nMissing values: {missing_values}")
print(f"Data completeness: {completeness:.1f}%")

if completeness == 100.0:
    print("🎉 100% data completeness achieved!")

combined_df_complete.head(10)

# Summary statistics
print("Summary Statistics:")
print("=" * 50)
print(combined_df.describe())

# Check for any obvious data quality issues
print("\nData Quality Checks:")
print("=" * 50)

# Check for negative values where they shouldn't exist
negative_wave_height = (combined_df['wave_height_m'] < 0).sum()
negative_wave_period = (combined_df['wave_period_s'] < 0).sum()
negative_wind_speed = (combined_df['wind_speed_knots'] < 0).sum()

print(f"Negative wave heights: {negative_wave_height}")
print(f"Negative wave periods: {negative_wave_period}")
print(f"Negative wind speeds: {negative_wind_speed}")

# Check direction ranges (should be 0-360)
invalid_wave_dir = ((combined_df['wave_direction_to_deg'] < 0) | (combined_df['wave_direction_to_deg'] > 360)).sum()
invalid_wind_dir = ((combined_df['wind_direction_from_deg'] < 0) | (combined_df['wind_direction_from_deg'] > 360)).sum()

print(f"Invalid wave directions (not 0-360): {invalid_wave_dir}")
print(f"Invalid wind directions (not 0-360): {invalid_wind_dir}")

# Check temperature ranges (reasonable for Malaga)
extreme_temps = ((combined_df['temperature_c'] < -10) | (combined_df['temperature_c'] > 50)).sum()
print(f"Extreme temperatures (<-10°C or >50°C): {extreme_temps}")

# Define output filenames
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
base_filename = f"malaga_surf_data_{timestamp}"

# Save individual datasets
wave_parquet = data_processed_path / f"wave_data_{timestamp}.parquet"
wind_parquet = data_processed_path / f"wind_data_{timestamp}.parquet"
combined_parquet = data_processed_path / f"{base_filename}.parquet"

wave_csv = data_processed_path / f"wave_data_{timestamp}.csv"
wind_csv = data_processed_path / f"wind_data_{timestamp}.csv"
combined_csv = data_processed_path / f"{base_filename}.csv"

# Save as Parquet (recommended for analysis)
print("Saving data as Parquet files...")
wave_df.to_parquet(wave_parquet, index=False)
wind_df.to_parquet(wind_parquet, index=False)
combined_df_complete.to_parquet(combined_parquet, index=False)

print(f"✓ Wave data saved to: {wave_parquet}")
print(f"✓ Wind data saved to: {wind_parquet}")
print(f"✓ Combined data (100% complete) saved to: {combined_parquet}")

# Save as CSV (for compatibility)
print("\nSaving data as CSV files...")
wave_df.to_csv(wave_csv, index=False)
wind_df.to_csv(wind_csv, index=False)
combined_df_complete.to_csv(combined_csv, index=False)

print(f"✓ Wave data saved to: {wave_csv}")
print(f"✓ Wind data saved to: {wind_csv}")
print(f"✓ Combined data saved to: {combined_csv}")

# Create a "latest" symlink for easy access
latest_parquet = data_processed_path / "malaga_surf_data_latest.parquet"
latest_csv = data_processed_path / "malaga_surf_data_latest.csv"

# Remove existing symlinks if they exist
if latest_parquet.is_symlink():
    latest_parquet.unlink()
if latest_csv.is_symlink():
    latest_csv.unlink()

# Create new symlinks
latest_parquet.symlink_to(combined_parquet.name)
latest_csv.symlink_to(combined_csv.name)

print("\n✓ Latest data accessible at:")
print(f"  - {latest_parquet}")
print(f"  - {latest_csv}")

# Final dataset info
print("Final Dataset Summary:")
print("=" * 50)
print(f"Total records: {len(combined_df_complete):,}")
print(f"Date range: {combined_df_complete['datetime'].min()} to {combined_df_complete['datetime'].max()}")
print(f"Time span: {(combined_df_complete['datetime'].max() - combined_df_complete['datetime'].min()).days} days")
print(f"Data completeness: {(1 - combined_df_complete.isnull().sum().sum() / (len(combined_df_complete) * len(combined_df_complete.columns))) * 100:.1f}%")
print(f"\nFiles saved to: {data_processed_path}")